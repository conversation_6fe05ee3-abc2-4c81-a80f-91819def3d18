{% extends 'base.html' %}

{% block title %}الصفحة الرئيسية{% endblock %}

{% block content %}
<!-- قسم الترحيب -->
<div class="row align-items-center justify-content-center py-4" style="min-height: 80vh;">
    <div class="col-lg-6 p-3 p-lg-5 pt-lg-3">
        <h1 class="display-5 mb-4 font-weight-bold text-primary">مرحباً بك في مهام Google</h1>
        <p class="lead text-muted">
            مهام Google هي خدمة مجانية من Google تتيح للمستخدم إنشاء مهام، تنظيمها، تحديد مواعيد نهائية لها، وربطها
            بتقويم Google. تساعد في تتبع المهام اليومية بسهولة من الهاتف أو الكمبيوتر، وتتكامل مع Gmail وGoogle Calendar
            لتوفير تجربة تنظيم متكاملة وبسيطة.
        </p>
        <div class="d-flex flex-column flex-md-row justify-content-md-start mb-lg-3 mt-4">
            <a href="{{ url_for('login') }}"
                class="btn btn-primary btn-lg px-4 mr-md-2 mb-2 mb-md-0 font-weight-bold">تسجيل
                الدخول</a>
            <a href="{{ url_for('register') }}" class="btn btn-outline-secondary btn-lg px-4">تسجيل جديد</a>
        </div>
    </div>
    <div class="col-lg-6 p-0 overflow-hidden">
        <img class="img-fluid rounded" src="{{ url_for('static', filename='images/8690687_3969308.svg') }}"
            alt="صورة توضيحية للمهام" style="width: 100%; height: auto; object-fit: cover;">
    </div>
</div>

<!-- قسم المزايا -->
<div class="container px-4 py-5" id="custom-cards">
    <h2 class="font-weight-bold h1 text-center mb-4">مزايا مهام Google</h2>
    <hr class="mb-5">

    <div class="row">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card card-cover h-100 overflow-hidden text-white shadow-lg"
                style="background-color:#407BFF !important;">
                <div class="card-body d-flex flex-column h-100 p-5 pb-3">
                    <h3 class="pt-5 mt-5 mb-4 h3 font-weight-bold">سهولة في الاستخدام، بسيط</h3>
                    <ul class="d-flex list-unstyled mt-auto">
                        <li class="mr-auto">
                            <img src="{{ url_for('static', filename='images/download.png') }}" alt="Bootstrap"
                                width="32" height="32" class="rounded-circle border border-white">
                        </li>
                        <li class="d-flex align-items-center mr-3">
                            <svg class="bi mr-2" width="1em" height="1em" role="img" aria-label="Location">
                                <use xlink:href="#geo-fill"></use>
                            </svg>
                            <small>سهل</small>
                        </li>
                        <li class="d-flex align-items-center">
                            <svg class="bi mr-2" width="1em" height="1em" role="img" aria-label="Duration">
                                <use xlink:href="#calendar3"></use>
                            </svg>
                            <small>Easy to use</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card card-cover h-100 overflow-hidden text-white shadow-lg"
                style="background-color: #ea4335 !important;">
                <div class="card-body d-flex flex-column h-100 p-5 pb-3">
                    <h3 class="pt-5 mt-5 mb-4 h3 font-weight-bold">إمكانية إضافة ملاحظات </h3>
                    <ul class="d-flex list-unstyled mt-auto">
                        <li class="mr-auto">
                            <img src="{{ url_for('static', filename='images/download.png') }}" width="32" height="32"
                                class="rounded-circle border border-white">
                        </li>
                        <li class="d-flex align-items-center mr-3"> <svg class="bi mr-2" width="1em" height="1em"
                                role="img" aria-label="Location">
                                <use xlink:href="#geo-fill"></use>
                            </svg> <small>تفاصيل</small> </li>
                        <li class="d-flex align-items-center"> <svg class="bi mr-2" width="1em" height="1em" role="img"
                                aria-label="Duration">
                                <use xlink:href="#calendar3"></use>
                            </svg> <small>Details</small> </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card card-cover h-100 overflow-hidden text-white shadow-lg"
                style="background-color: #34A853 !important;">
                <div class="card-body d-flex flex-column h-100 p-5 pb-3">
                    <h3 class="pt-5 mt-5 mb-4 h3 font-weight-bold">مجاني تمامًا , لا يحتاج لاشتراك</h3>
                    <ul class="d-flex list-unstyled mt-auto">
                        <li class="mr-auto"> <img src="{{ url_for('static', filename='images/download.png') }}"
                                alt="Bootstrap" width="32" height="32" class="rounded-circle border border-white"> </li>
                        <li class="d-flex align-items-center mr-3"> <svg class="bi mr-2" width="1em" height="1em"
                                role="img" aria-label="Location">
                                <use xlink:href="#geo-fill"></use>
                            </svg> <small>مجاني</small> </li>
                        <li class="d-flex align-items-center"> <svg class="bi mr-2" width="1em" height="1em" role="img"
                                aria-label="Duration">
                                <use xlink:href="#calendar3"></use>
                            </svg> <small>free</small> </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}